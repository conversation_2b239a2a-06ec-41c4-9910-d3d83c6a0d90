<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body>
    <div id="header">
        <div class="header-texts"> 
            <h2>Your Score: <span id="score">0</span></h2>           
        </div>
        <!-- Other header elements here -->
    </div>
    <div id="user-form-container" style="display: none;">
      <h2>Please fill out your information below</h2>
      <form id="user-form">
        <div class="form-row">
          <div class="form-group">
            <input type="text" id="first-name" required>
            <label for="first-name">First Name</label>
          </div>
          <div class="form-group">
            <input type="text" id="last-name" required>
            <label for="last-name">Last Name</label>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <input type="email" id="email" required>
            <label for="email">Email</label>
          </div>
          <div class="form-group">
            <input type="tel" id="phone" required>
            <label for="phone">Phone Number</label>
          </div>
        </div>
        <div class="form-group">
          <input type="text" id="company" required>
          <label for="company">Company</label>
        </div>
        <div class="form-group">
          <input type="text" id="role" required>
          <label for="role" class="input-label">Role e.g., HR Manager, CEO, Marketing Specialist</label>
        </div>
        
        <button type="submit" id="submit-form">See results</button>
      </form>
    </div>

    <div id="consent-container" class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 hidden p-4">
      <div class="bg-white rounded-lg p-6 max-w-md w-full relative">
        <button id="close-consent-btn" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <h2 class="text-xl font-bold mb-4">Congratulations, you've finished!</h2>
        <p class="mb-4 text-sm md:text-base">To make your results even more helpful, we'd love some quick information about you. This will allow us to provide personalised feedback and recommendations tailored to your needs.</p>
        <p class="mb-4 text-sm md:text-base font-medium" style="color: #68C692;">Rest assured, your information will only be used to contact you about your assessment.</p>
        <div class="mb-4">
          <input type="checkbox" id="consent-checkbox" class="mr-2">
          <label for="consent-checkbox" class="text-sm md:text-base">
            I consent to the collection and processing of my personal data as explained in the
            <a href="https://barefootelearning.com/privacy-policy-2/" target="_blank" class="hover:underline" style="color: #68C692;">Privacy Policy</a>.
          </label>
        </div>
        <button id="consent-btn" class="w-full md:w-auto text-white px-4 py-2 rounded text-sm md:text-base" style="background-color: #68C692;">Proceed</button>
      </div>
    </div>


    <div id="start-page">
      <img src="icglogo.png" alt="SGA Logo" style="width: 200px; height: auto; margin: 1rem auto;" class="mx-auto">
    <p>Take our quick questionnaire and test your Digital Skills knowledge.</p>
    <div id="terms-container">
        <input type="checkbox" id="terms-checkbox" required>
        <label for="terms-checkbox">I agree to the <a href="https://barefootelearning.com/privacy-policy-2" target="_blank">Privacy Policy</a> for data collection.</label>
      </div>
        <button id="start-btn">START</button>
    </div>


    <div id="quiz-container" class="hidden flex flex-col justify-center items-center">
      <div id="progress-bar-text" class="mb-4 text-center">0%</div>
      <div id="progress-bar" class="h-5 w-full bg-gray-300 rounded-lg overflow-hidden mb-4">
          <div id="progress-bar-fill" class="h-full" style="width: 0%; background-color: #68C692;"></div>
      </div>
      <p id="message" class="mb-4 text-center"></p>
      <h2 id="question" class="text-lg mb-4 text-center"></h2>
      <div id="options-container" class="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
          <button class="option-btn text-white py-2 px-4 rounded-full mb-2" id="btn0" style="background-color: #68C692;"></button>
          <button class="option-btn text-white py-2 px-4 rounded-full mb-2" id="btn1" style="background-color: #68C692;"></button>
          <button class="option-btn text-white py-2 px-4 rounded-full mb-2" id="btn2" style="background-color: #68C692;"></button>
          <button class="option-btn text-white py-2 px-4 rounded-full mb-2" id="btn3" style="background-color: #68C692;"></button>
      </div>
      <button class="skip-btn text-white py-2 px-4 rounded-full mb-2" id="skip-btn" style="background-color: #68C692;">I Don't Know</button>
      <button id="next-btn" class="bg-green-500 text-white py-2 px-4 rounded-full mb-2 hidden">Next Question</button>
  </div>
      <div id="result-container" style="display: none">
        <h2 id="result-text"></h2>
        <h3 id="remarks"></h3>
        <p id="comments"></p>
        <p class="book-text">
          <span class="regular-text">To have access to our Full Assessment Tool FREE and determine the best courses to improve your productivity, click the button below</span>
        </p>
        <p class="book-text">
          <span class="regular-text"><strong>Give it a try today</strong></span>
          <br>
          <a id="try-it-link" href="https://barefootassesment.com/try-it" target="_blank">Click Here</a>
        </p>
        <p>We will send you the link to our Full Assessment Tool and on completion, you will receive a suggested learning pathway with the names of the online courses you should take to improve your skills.</p>
        <p class="book-text">
          <span class="regular-text">And if you want to know more about our online courses for business click the links below.</span>
          <br>
          <a href="https://barefootelearning.com/microlearning/#individual-courses"target="_blank">IT SKILLS</a>
          <br>
          <a href="https://barefootelearning.com/microlearning/soft-skills/"target="_blank">SOFT SKILLS</a>
        </p>
        <p class="book-text">
          <span class="regular-text">Or book a meeting with us to talk about your learning requirements</span>
        </p>
        <button class="book-btn" onclick="openBookingPage()">BOOK NOW</button>
      </div>

      <div id="confirmation-container" style="display: none">
        <h2>Thank you for your information.</h2>
        <h3>An email containing a link to start your assessment will be dispatched to you shortly📩.</h3>
      </div>

      <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center" style="display: none;">
        <div class="loading-spinner border-4 border-gray-300 border-t-4 rounded-full w-8 h-8 animate-spin" style="border-top-color: #68C692;"></div>
        <div class="loading-text">Saving your response...</div>
      </div>
    
    <div id="modal-loading-overlay">
      <div class="modal-loading-content">
        <div class="modal-spinner-container">
          <svg class="modal-circular-progress" width="120" height="120" viewBox="0 0 120 120">
            <circle class="modal-progress-background" cx="60" cy="60" r="54" />
            <circle class="modal-progress-bar" cx="60" cy="60" r="54" />
          </svg>
          <div class="modal-progress-percentage">0%</div>
        </div>
        <div class="modal-loading-text">Processing skills gap analysis...</div>
      </div>
    </div>
    <script src="litescript.js"></script>
</body>
</html>