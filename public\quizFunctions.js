(function(global) {
    // Client-side request tracking to prevent duplicate requests
    const pendingRequests = new Set();

    // Global framework cache to share between components
    if (!window.frameworkCache) {
      window.frameworkCache = {};
    }

    // Global question cache to prevent duplicate API requests
    if (!window.questionCache) {
      window.questionCache = {};
    }

    // Helper function to check if a request is pending
    function isRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      return pendingRequests.has(requestKey);
    }

    // Helper function to mark a request as pending
    function markRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      pendingRequests.add(requestKey);
      console.log(`Request marked as pending: ${requestKey}`);
      return requestKey;
    }

    // Helper function to mark a request as completed
    function markRequestCompleted(requestKey) {
      pendingRequests.delete(requestKey);
      console.log(`Request marked as completed: ${requestKey}`);
    }

    // Helper function to cache framework data
    function cacheFramework(role, framework) {
      window.frameworkCache[role] = framework;
      console.log(`Framework cached for role: ${role}`);
    }

    // Helper function to get cached framework data
    function getCachedFramework(role) {
      return window.frameworkCache[role];
    }

    // Helper function to cache questions
    function cacheQuestions(role, section, type, questions) {
      const cacheKey = `${role}_${section}_${type}`;
      window.questionCache[cacheKey] = questions;
      console.log(`Cached ${type} questions for ${role}/${section}`);
    }

    // Helper function to get cached questions
    function getCachedQuestions(role, section) {
      const cacheKey = `${role}_${section}_regular`;
      return window.questionCache[cacheKey];
    }

    // Helper function to get cached self-assessment questions
    function getCachedSelfAssessmentQuestions(role, section) {
      const cacheKey = `${role}_${section}_self-assessment`;
      return window.questionCache[cacheKey];
    }

    const startQuiz = () => {
        // Reset all scores and global state variables
        currentQuestion = 0;
        score = 0;
        sectionScores = {
          "Essentials": 0,
          "Intermediate": 0,
          "Advanced": 0,
          "Champions": 0
        };
        questionsPerSection = {
          "Essentials": 0,
          "Intermediate": 0,
          "Advanced": 0,
          "Champions": 0
        };
        isFinalSuccessContainerDisplayed = false;
        isFailureContainerDisplayed = false;

        document.getElementById("start-page").style.display = "none";
        document.getElementById("consent-popup").style.display = "block";
      };


const endQuiz = async () => {
  const finalResults = window.quizLogger ? window.quizLogger.logFinalResults() : null;
  document.getElementById("quiz-container").style.display = "none";

  // Calculate score based only on knowledge-check questions
  const knowledgeCheckQuestions = quizData.filter(q => q.type === "knowledge-check");
  const totalKnowledgeQuestions = knowledgeCheckQuestions.length;
  const passThreshold = 0.7 * totalKnowledgeQuestions;

  if (score >= passThreshold) {
    if (currentSection === 4) {
        const email = document.getElementById("email").value.trim();
        if (email) {
            try {
                await db.collection('assessmentStatus').doc(email).update({
                    status: 'completed',
                    timestamp: firebase.firestore.FieldValue.serverTimestamp()
                });
            } catch (error) {
                console.error('Error updating assessment status to Firestore:', error);
                showNotification('Error updating assessment status. Please try again.', 'error');
            }
        }
        isFinalSuccessContainerDisplayed = true;
        document.getElementById("final-success-container").style.display = "block";
    } else {
        document.getElementById("success-container").style.display = "block";
        document.getElementById("current-section").innerText = currentSection;
        const successHeading = document.getElementById("success-heading");
        successHeading.innerText = `You passed Section ${currentSection}: ${sectionNames[currentSection - 1]}`;
    }
  } else {
    showLoadingOverlay();
    document.getElementById("failure-container").style.display = "block";
    isFailureContainerDisplayed = true;

    const email = document.getElementById("email").value.trim();

    if (email) {
        try {
            const firstName = document.getElementById("first-name").value.trim();
            const lastName = document.getElementById("last-name").value.trim();
            const phone = document.getElementById("phone").value.trim();
            const role = document.getElementById("role").value.trim();

            // Create the assessment summary object
            const assessmentSummary = {
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                totalScore: score,
                totalKnowledgeQuestions: totalKnowledgeQuestions,
                sectionScores: { ...sectionScores },
                questionsPerSection: { ...questionsPerSection },
                currentSection: currentSection,
                status: 'completed'
            };

            const resultData = {
                employeeEmail: email,
                section: sectionNames[currentSection - 1],
                score,
                role,
                totalQuestions: totalKnowledgeQuestions, // Only count knowledge questions for scoring
                isNewUser: false,
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                firstName,
                lastName,
                userCompany: userCompany,
                userPhone: phone,
            };

            const companyRef = db.collection('companies').doc(userCompany);
            const userRef = companyRef.collection('users').doc(email);
            const assessmentResultsRef = userRef.collection('assessmentResults_ai');

            // Save the assessment summary to a new collection
            await userRef.collection('assessmentSummaries_ai').add(assessmentSummary);
            console.log('Assessment summary saved to Firestore:', assessmentSummary);

            // Add the new assessment result
            await assessmentResultsRef.add(resultData);
            console.log('Assessment result saved to Firestore:', resultData);

            // Update the assessment status under the user document
            await userRef.update({ status: 'completed' });

            hideLoadingOverlay();
            showFeedbackPrompt();
            initializePathwayButton();
            pollForRecommendations(email, userCompany);

            showNotification('Your results are being processed. You will be notified when complete.', 'success');

            sendAssessmentResult(email).then(() => {
                console.log('Assessment result sent for recommendations');
                showNotification('Your results have been processed successfully!', 'success');
            }).catch(error => {
                console.error('Error sending assessment result for recommendations:', error);
                showNotification('An error occurred while processing your results. Please contact support.', 'error');
            });

        } catch (error) {
            console.error('Error saving assessment data to Firestore:', error);
            hideLoadingOverlay();
            showNotification('An error occurred while submitting assessment data. Please try again or contact support.', 'error');
        }
    } else {
        console.log('Email is not provided, assessment data will not be saved.');
        hideLoadingOverlay();
    }
  }
};

      const logFinalResults = () => {
        const currentSectionName = sectionNames[currentSection - 1];

        // Filter responses to only include current section
        const currentSectionResponses = sessionLogs.userResponses.filter(response =>
            response.section === currentSectionName
        );

        const finalResults = {
            timestamp: new Date().toISOString(),
            userInfo: {
                email: document.getElementById("email").value.trim(),
                firstName: document.getElementById("first-name").value.trim(),
                lastName: document.getElementById("last-name").value.trim(),
                role: document.getElementById("role").value.trim(),
                company: userCompany
            },
            quizResults: {
                totalScore: score,
                sectionScores: { [currentSectionName]: sectionScores[currentSectionName] },
                questionsPerSection: { [currentSectionName]: questionsPerSection[currentSectionName] },
                currentSection: currentSection,
                sectionName: currentSectionName,
                totalQuestions: quizData.length,
                passThreshold: 0.7 * quizData.length,
                passed: score >= (0.7 * quizData.length)
            },
            completeSessionData: {
                framework: sessionLogs.framework,
                quiz: sessionLogs.quiz,
                userResponses: currentSectionResponses,
                finalTimestamp: new Date().toISOString()
            }
        };

        console.log('Final Quiz Results:', JSON.stringify(finalResults, null, 2));
        return finalResults;
    };

    // Replace the loadQuizData function in quizFunctions.js
const loadQuizData = async () => {
  showQuizLoadingOverlay();

  let currentProgress = 0;
  updateLoadingProgress(currentProgress);

  const progressSteps = [
    { target: 20, duration: 1000 },
    { target: 40, duration: 2000 },
    { target: 60, duration: 2000 },
    { target: 80, duration: 2000 },
    { target: 90, duration: 1000 }
  ];

  let currentStep = 0;

  const animateProgress = (startValue, targetValue, duration) => {
    const startTime = Date.now();
    const animate = () => {
      const currentTime = Date.now();
      const elapsed = currentTime - startTime;

      if (elapsed < duration) {
        // Use easeInOutCubic for smoother animation
        const progress = elapsed / duration;
        const eased = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        currentProgress = startValue + (targetValue - startValue) * eased;
        updateLoadingProgress(Math.round(currentProgress));
        requestAnimationFrame(animate);
      } else {
        currentProgress = targetValue;
        updateLoadingProgress(Math.round(currentProgress));
        if (currentStep < progressSteps.length - 1) {
          currentStep++;
          animateProgress(
            currentProgress,
            progressSteps[currentStep].target,
            progressSteps[currentStep].duration
          );
        }
      }
    };
    requestAnimationFrame(animate);
  };

  // Start the first animation
  animateProgress(0, progressSteps[0].target, progressSteps[0].duration);

  try {
    const role = document.getElementById("role").value.trim();
    const currentSectionName = sectionNames[currentSection - 1];

    showQuestionLoadingState();

    // Try to get the framework from our global cache first
    let framework = getCachedFramework(role);

    // If not in cache, try to get it from Firestore
    if (!framework) {
      console.log('Framework not found in cache, fetching from Firestore');
      const frameworkDoc = await db.collection('frameworks')
        .doc(role)
        .get();

      if (!frameworkDoc.exists) {
        throw new Error('Framework not found for role');
      }

      framework = frameworkDoc.data();

      // Cache it for future use
      cacheFramework(role, framework);
    } else {
      console.log('Using cached framework for role:', role);
    }

    // Get email for session tracking
    const email = document.getElementById("email")?.value.trim() || null;

    // Check if requests are already pending
    const quizParams = { role, section: currentSectionName };
    const selfAssessParams = { role, section: currentSectionName };

    if (isRequestPending('generate-quiz', quizParams) ||
        isRequestPending('generate-self-assessment', selfAssessParams)) {
      console.log('Requests already pending, waiting for completion...');

      // Instead of throwing an error, update the loading message
      const loadingMessage = document.getElementById('quiz-loading-message');
      if (loadingMessage) {
        loadingMessage.textContent = 'Questions are being processed. Please wait a moment...';
      }

      // Update progress indicator to show we're waiting
      currentProgress = 40; // Set to a medium progress value
      updateLoadingProgress(currentProgress);

      // Poll for completion of the pending requests
      let waitTime = 0;
      const pollInterval = 2000; // Check every 2 seconds
      const maxWaitTime = 45000; // 45 seconds maximum wait time

      return new Promise((resolve, reject) => {
        const checkCompletion = setInterval(() => {
          waitTime += pollInterval;

          if (!isRequestPending('generate-quiz', quizParams) &&
              !isRequestPending('generate-self-assessment', selfAssessParams)) {
            // Requests completed, try to get cached data
            clearInterval(checkCompletion);

            // Smoothly complete loading progress
            animateProgress(currentProgress, 100, 1000);

            setTimeout(() => {
              // Get the cached questions instead of making new API requests
              const cachedRegularQuestions = getCachedQuestions(role, currentSectionName);
              const cachedSelfAssessmentQuestions = getCachedSelfAssessmentQuestions(role, currentSectionName);

              if (cachedRegularQuestions && cachedSelfAssessmentQuestions) {
                console.log('Using cached questions from completed requests');

                // Mark regular questions with a type
                const typedRegularQuestions = cachedRegularQuestions.map(q => ({
                  ...q,
                  type: "knowledge-check"
                }));

                // Combine and shuffle the questions
                quizData = [...typedRegularQuestions, ...cachedSelfAssessmentQuestions];
                quizData = shuffleArray(quizData);

                questionsPerSection[currentSectionName] = quizData.length;
                sectionScores[currentSectionName] = 0;

                document.getElementById("current-section").innerText = currentSection;
                document.getElementById("section-name").innerText = currentSectionName;

                // Log the quiz data for tracking
                if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
                  window.quizLogger.logQuiz(quizData);
                }

                // Hide loading overlay
                hideQuizLoadingOverlay();

                // Load the question and update the progress bar
                loadQuestion();
                updateProgressBar();

                console.log('Quiz UI displayed successfully using cached data');
                resolve();
              } else {
                // If for some reason we don't have cached data, try again
                console.warn('Cached questions not found after request completion');
                hideQuizLoadingOverlay();
                loadQuizData().then(resolve).catch(reject);
              }
            }, 1200);

          } else if (waitTime >= maxWaitTime) {
            // Timeout reached, still pending
            clearInterval(checkCompletion);

            if (loadingMessage) {
              loadingMessage.textContent = 'Taking longer than expected. Retrying...';
            }

            // Show a message to the user
            showNotification('Questions are taking longer than expected to load. Retrying...', 'info');

            setTimeout(() => {
              hideQuizLoadingOverlay();
              loadQuizData().then(resolve).catch(reject);
            }, 1500);

          } else {
            // Still waiting, update progress
            currentProgress = Math.min(85, currentProgress + 3);
            updateLoadingProgress(currentProgress);

            if (loadingMessage && waitTime > 10000) { // After 10 seconds show countdown
              loadingMessage.textContent = `Still preparing questions (${Math.round((maxWaitTime - waitTime)/1000)}s remaining)...`;
            }
          }
        }, pollInterval);
      });
    }

    // Mark requests as pending
    const quizRequestKey = markRequestPending('generate-quiz', quizParams);
    const selfAssessRequestKey = markRequestPending('generate-self-assessment', selfAssessParams);

    // Update loading message to show we're fetching both types of questions
    const loadingMessage = document.getElementById('quiz-loading-message');
    if (loadingMessage) {
      loadingMessage.textContent = 'Preparing your personalized assessment...';
    }

    // Important: Update progress to indicate we're fetching questions
    animateProgress(currentProgress, 40, 1000);

    // Create state tracking variables for question loading
    let regularQuestionsReceived = false;
    let selfAssessmentQuestionsReceived = false;
    let regularQuestions = [];
    let selfAssessmentQuestions = [];

    console.log('Starting parallel fetch of both question types...');

    // Fetch both standard quiz questions and self-assessment questions in parallel
    // but handle each response separately to track when each type is received
    const regularQuestionsPromise = fetch('/api/generate-quiz', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role,
        section: currentSectionName,
        framework,
        email  // Include email as session identifier
      }),
    });

    const selfAssessmentPromise = fetch('/api/generate-self-assessment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role,
        section: currentSectionName,
        framework,
        email  // Include email as session identifier
      }),
    });

    // Wait for both promises to resolve
    const [regularQuestionsResponse, selfAssessmentResponse] = await Promise.all([regularQuestionsPromise, selfAssessmentPromise]);

    // Update loading message to indicate we're now processing the responses
    if (loadingMessage) {
      loadingMessage.textContent = 'Received responses, processing questions...';
    }

    // Update progress to indicate we've received responses and are processing
    animateProgress(40, 60, 1000);

    // Process regular questions response
    if (!regularQuestionsResponse.ok) {
      const errorData = await regularQuestionsResponse.json().catch(() => ({}));
      throw new Error(`Server error (regular questions): ${errorData.error || regularQuestionsResponse.statusText}`);
    } else {
      console.log('Regular questions response received successfully');
      regularQuestions = await regularQuestionsResponse.json();
      regularQuestionsReceived = true;

      if (loadingMessage) {
        loadingMessage.textContent = 'Knowledge check questions received, waiting for self-assessment...';
      }

      // Validate regular questions
      if (!Array.isArray(regularQuestions) || regularQuestions.length !== 10) {
        throw new Error('Invalid regular question data received');
      }
      console.log('Regular questions validated successfully');

      // Cache the regular questions
      cacheQuestions(role, currentSectionName, 'regular', regularQuestions);
    }

    // Process self-assessment questions response
    if (!selfAssessmentResponse.ok) {
      const errorData = await selfAssessmentResponse.json().catch(() => ({}));
      throw new Error(`Server error (self-assessment): ${errorData.error || selfAssessmentResponse.statusText}`);
    } else {
      console.log('Self-assessment questions response received successfully');
      selfAssessmentQuestions = await selfAssessmentResponse.json();
      selfAssessmentQuestionsReceived = true;

      if (loadingMessage) {
        loadingMessage.textContent = 'Self-assessment questions received, preparing UI...';
      }

      // Validate self-assessment questions
      if (!Array.isArray(selfAssessmentQuestions) || selfAssessmentQuestions.length !== 5) {
        throw new Error('Invalid self-assessment question data received');
      }
      console.log('Self-assessment questions validated successfully');

      // Cache the self-assessment questions
      cacheQuestions(role, currentSectionName, 'self-assessment', selfAssessmentQuestions);
    }

    // Ensure both types of questions have been received before proceeding
    if (!regularQuestionsReceived || !selfAssessmentQuestionsReceived) {
      throw new Error('Failed to receive all required question types');
    }

    console.log('Both question types received and validated');

    // Update loading message to indicate we're preparing the UI
    if (loadingMessage) {
      loadingMessage.textContent = 'All questions received, preparing UI...';
    }

    // Update progress to indicate we're about to prepare the UI
    animateProgress(60, 80, 800);

    // Update progress to indicate we're preparing the UI
    animateProgress(80, 95, 500);

    // Mark regular questions with a type
    const typedRegularQuestions = regularQuestions.map(q => ({
      ...q,
      type: "knowledge-check"
    }));

    // Combine and shuffle the questions
    quizData = [...typedRegularQuestions, ...selfAssessmentQuestions];
    quizData = shuffleArray(quizData);

    questionsPerSection[currentSectionName] = quizData.length;
    sectionScores[currentSectionName] = 0;

    document.getElementById("current-section").innerText = currentSection;
    document.getElementById("section-name").innerText = currentSectionName;

    // Log the quiz data for tracking
    if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
      window.quizLogger.logQuiz(quizData);
    }

    // Final progress update before showing the UI
    animateProgress(95, 100, 300);

    console.log('All questions processed, preparing to display UI');
    console.log(`Total questions: ${quizData.length} (${typedRegularQuestions.length} knowledge check, ${selfAssessmentQuestions.length} self-assessment)`);

    // Only now, AFTER all data is prepared, do we display the first question
    // This ensures the UI only loads once with the complete set of questions
    setTimeout(() => {
      // Hide loading overlay first
      hideQuizLoadingOverlay();

      // Then load the question and update the progress bar
      loadQuestion();
      updateProgressBar();

      // Mark requests as completed
      markRequestCompleted(quizRequestKey);
      markRequestCompleted(selfAssessRequestKey);

      console.log('Quiz UI displayed successfully');
    }, 500);

  } catch (error) {
    console.error('Error loading quiz data:', error);

    // If we marked requests as pending but they failed, release them
    if (typeof quizRequestKey !== 'undefined') {
      markRequestCompleted(quizRequestKey);
    }
    if (typeof selfAssessRequestKey !== 'undefined') {
      markRequestCompleted(selfAssessRequestKey);
    }

    const errorMessage = error.message.includes('Network error')
      ? 'Connection error. Please check your internet connection and try again.'
      : error.message === 'Requests already in progress. Please wait.'
        ? 'Questions are already being loaded. Please wait.'
        : 'Failed to load quiz questions. Please try again.';

    showNotification(errorMessage, 'error');

    document.getElementById("question").innerText = "Error loading questions. Please try again.";
    document.querySelectorAll('.option-btn').forEach(btn => {
      btn.disabled = true;
      btn.innerText = '-';
    });

    // Still need to hide loading overlay even on error
    hideQuizLoadingOverlay();
  }
};

// Helper function to shuffle an array (Fisher-Yates algorithm)
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}


      function showQuestionLoadingState() {
        document.getElementById("question").innerText = "Loading questions...";
        document.getElementById("progress-bar-fill").style.width = "0%";
        document.getElementById("progress-bar-text").innerText = "0%";

        const optionButtons = document.querySelectorAll('.option-btn');
        optionButtons.forEach(btn => {
          btn.disabled = true;
          btn.innerText = 'Loading...';
        });

        document.getElementById("skip-btn").disabled = true;
        document.getElementById("message").innerText = "";
      }

      const loadQuestion = () => {
        const questionObj = quizData[currentQuestion];
        document.getElementById("question").innerText = questionObj.question;

        // Get the options container
        const optionsContainer = document.getElementById("options-container");

        // Apply question type specific styling
        if (questionObj.type === "self-assessment") {
          optionsContainer.classList.add("self-assessment");
          // Show a subtle indicator for self-assessment questions
          document.getElementById("message").innerHTML = '<span class="self-assessment-indicator">Self-Assessment Question</span>';
        } else {
          optionsContainer.classList.remove("self-assessment");
          document.getElementById("message").innerText = "";
        }

        // Handle different number of options (3 for self-assessment, 4 for knowledge check)
        const optionCount = questionObj.type === "self-assessment" ? 3 : 4;

        // Display options
        for (let i = 0; i < 4; i++) {
          const btn = document.getElementById(`btn${i}`);

          if (i < optionCount) {
            btn.innerText = questionObj.options[i];
            btn.className = "option-btn";
            btn.dataset.type = questionObj.type;
            btn.dataset.level = i + 1; // Store level for self-assessment (1=basic, 2=intermediate, 3=advanced)
            btn.disabled = false;
            btn.style.opacity = 1;
            btn.style.cursor = "pointer";
            btn.style.display = "block";
          } else {
            // Hide extra buttons if not needed (for self-assessment with 3 options)
            btn.style.display = "none";
          }
        }

        document.getElementById("skip-btn").disabled = false;
        document.getElementById("skip-btn").style.opacity = 1;
        document.getElementById("skip-btn").style.cursor = "pointer";
      };

        const restartQuiz = () => {
        currentQuestion = 0;
        score = 0;
        document.getElementById("score").innerText = "0";
        document.getElementById("failure-container").style.display = "none";
        document.getElementById("start-page").style.display = "block";
        document.getElementById("progress-bar-fill").style.width = "0%";
        document.getElementById("progress-bar-text").innerText = "0%";
        loadQuizData();
      };


    // Toast notification function from the framework
    function showToast(message, duration = 5000) {
        const toast = document.createElement('div');
        toast.classList.add('toast');
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);
    }

    function updateProgressBar() {
      const progress = (currentQuestion / quizData.length) * 100;
      console.log('Progress:', progress);

      // Update progress bar fill
      const progressBarFill = document.getElementById("progress-bar-fill");
      progressBarFill.style.width = `${progress}%`;

      // Check if progress text container exists
      let progressBarText = document.getElementById("progress-bar-text");

      if (!progressBarText) {
        // Create a new container for the progress text if it doesn't exist
        progressBarText = document.createElement("div");
        progressBarText.id = "progress-bar-text";
        progressBarText.className = "text-center text-gray-700 mb-2"; // Added proper styling classes

        // Get the progress bar container
        const progressBar = document.getElementById("progress-bar");
        // Insert the text BEFORE the progress bar
        progressBar.parentNode.insertBefore(progressBarText, progressBar);
      }

      // Update the progress text
      progressBarText.textContent = `Progress: ${Math.round(progress)}%`;
    }

    global.startQuiz = startQuiz;
    global.endQuiz = endQuiz;
    global.loadQuizData = loadQuizData;
    global.loadQuestion = loadQuestion;
    global.updateProgressBar = updateProgressBar;
    global.restartQuiz = restartQuiz;
    global.logFinalResults = logFinalResults;
    window.showToast = showToast;
  })(typeof window !== 'undefined' ? window : global);
