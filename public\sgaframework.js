(function(global) {
    let frameworkData = null;

    // Client-side request tracking to prevent duplicate requests
    const pendingRequests = new Set();

    // Global framework cache to share between components
    if (!window.frameworkCache) {
      window.frameworkCache = {};
    }

    // Helper function to check if a request is pending
    function isRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      return pendingRequests.has(requestKey);
    }

    // Helper function to mark a request as pending
    function markRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      pendingRequests.add(requestKey);
      console.log(`Request marked as pending: ${requestKey}`);
      return requestKey;
    }

    // Helper function to mark a request as completed
    function markRequestCompleted(requestKey) {
      pendingRequests.delete(requestKey);
      console.log(`Request marked as completed: ${requestKey}`);
    }

    // Helper function to cache framework data
    function cacheFramework(role, framework) {
      window.frameworkCache[role] = framework;
      console.log(`Framework cached for role: ${role}`);
    }

    // Helper function to get cached framework data
    function getCachedFramework(role) {
      return window.frameworkCache[role];
    }

    function showToast(message, duration = 5000) {
        const toast = document.createElement('div');
        toast.classList.add('toast');
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);
    }

    async function createSkillsGapAnalyzer(containerId, options) {
        var container = document.getElementById(containerId);
        if (!container) {
            console.error('Container element not found');
            return;
        }

        // Set default options if not provided
        options = options || {};
        options.logoUrl = options.logoUrl || 'icglogo.png'; // Updated to use new SGA logo

        // Inject styles
        injectStyles();

        // Create UI elements
        createUI(container, options);

        // Set up event listeners
        setupEventListeners();

        // Initialize content with loading state
        setActiveSection('role');
    }

    function injectStyles() {
        var style = document.createElement('style');
        style.type = 'text/css';
        style.innerHTML = `
            .skills-gap-analyzer {
                font-family: 'Montserrat', sans-serif;
                color: #333333;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                background-size: cover;
                background-position: center;
                padding: 20px;
            }

            .glass-container {
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                width: 100%;
                max-width: 800px;
                margin: 20px auto;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            .framework-header {
                background-color: #68C692;
                padding: 1.5rem;
                display: flex;
                align-items: center;
            }

            .framework-logo {
                height: 2.5rem;
            }

            .framework-title {
                margin-left: 1rem;
                color: #ffffff;
                font-size: 0.875rem;
            }

            .content-wrapper {
                flex: 1;
                padding: 1.5rem;
                background: rgba(255, 255, 255, 0.95);
                overflow-y: auto;
                max-height: 70vh;
            }

            .nav-container {
                display: flex;
                gap: 1rem;
                margin-bottom: 1.5rem;
                overflow-x: auto;
                padding-bottom: 0.5rem;
            }

            .nav-button {
                padding: 0.5rem 1rem;
                border-radius: 0.375rem;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
                white-space: nowrap;
            }

            .nav-button.active {
                background-color: #68C692;
                color: white;
            }

            .nav-button:not(.active) {
                background-color: transparent;
                color: #68C692;
            }

            .nav-button:hover:not(.active) {
                background-color: #e5e7eb;
            }

            .section-content {
                animation: fadeIn 0.3s ease-in-out;
            }

            .competency-card {
                background: white;
                border-radius: 0.5rem;
                padding: 1rem;
                margin-bottom: 1rem;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .competency-title {
                color: #68C692;
                font-size: 1rem;
                font-weight: 500;
                margin-bottom: 0.5rem;
            }

            .skills-list {
                list-style-type: disc;
                margin-left: 1.5rem;
            }

            .chart-container {
                width: 100%;
                max-width: 400px;
                margin: 1rem auto;
                height: 300px;
            }

            .proceed-button {
                background-color: #68C692;
                color: white;
                padding: 0.75rem 1.5rem;
                border-radius: 0.375rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin: 1.5rem auto;
                cursor: pointer;
                border: none;
                transition: background-color 0.2s;
            }

            .proceed-button:hover {
                background-color: #8DCE8C;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .loading {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                color: #666666;
            }

            @keyframes shimmer {
                0% {
                    background-position: -1000px 0;
                }
                100% {
                    background-position: 1000px 0;
                }
            }

            .skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 1000px 100%;
                animation: shimmer 2s infinite linear;
            }

            .skeleton-text {
                height: 12px;
                margin-bottom: 6px;
                border-radius: 4px;
            }

            .skeleton-title {
                height: 20px;
                width: 60%;
                margin-bottom: 12px;
                border-radius: 4px;
            }

            .skeleton-card {
                height: 60px;
                margin-bottom: 10px;
                border-radius: 8px;
            }

            .toast {
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #68C692;
                color: white;
                padding: 16px;
                border-radius: 4px;
                font-size: 16px;
                opacity: 0;
                transition: opacity 0.3s ease-in-out;
                z-index: 9999;
                max-width: 300px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            .toast.show {
                opacity: 1;
            }

            /* Progress Bar Styles */
            .progress-bar-container {
                position: relative;
                width: 100%;
                height: 20px;
                background-color: #e5e7eb;
                border-radius: 10px;
                overflow: hidden;
                margin: 20px 0;
            }

            .progress-bar-inner {
                height: 100%;
                width: 0%;
                background-color: #68C692;
                border-radius: 10px;
                transition: width 0.2s ease-in-out;
            }

            .progress-bar-text {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 0.875rem;
                color: #ffffff;
                font-weight: 500;
            }

            /* Mobile Responsive Styles */
            @media (max-width: 768px) {
                .glass-container {
                    margin: 10px;
                    width: calc(100% - 20px);
                }

                .framework-header {
                    padding: 1rem;
                    flex-direction: column;
                    text-align: center;
                }

                .framework-title {
                    margin: 0.5rem 0 0 0;
                }

                .content-wrapper {
                    padding: 1rem;
                    max-height: calc(100vh - 150px);
                }

                .nav-container {
                    flex-wrap: wrap;
                    justify-content: center;
                    gap: 0.5rem;
                }

                .nav-button {
                    padding: 0.4rem 0.8rem;
                    font-size: 0.8rem;
                    flex: 1 1 calc(50% - 0.5rem);
                    text-align: center;
                    min-width: 120px;
                }

                .competency-card {
                    padding: 0.75rem;
                }

                .competency-title {
                    font-size: 0.9rem;
                }

                .skills-list {
                    margin-left: 1rem;
                    font-size: 0.85rem;
                }

                .chart-container {
                    max-width: 100%;
                    height: 250px;
                }

                .proceed-button {
                    width: 100%;
                    justify-content: center;
                    padding: 0.6rem 1rem;
                    font-size: 0.9rem;
                }

                .toast {
                    left: 20px;
                    right: 20px;
                    max-width: none;
                    text-align: center;
                }
            }
            `;
        document.head.appendChild(style);
    }

    function createUI(container, options) {
        container.innerHTML = `
            <div class="skills-gap-analyzer" style="background-image: url(${options.backgroundImage})">
                <div class="glass-container">
                    <div class="framework-header">
                        <img src="${options.logoUrl}" alt="Skills Gap Analyzer Logo" class="framework-logo">
                        <p class="framework-title">Pre-assessment Framework</p>
                    </div>

                    <div class="content-wrapper">
                        <!-- Progress Bar -->
                        <div id="progress-bar-container" class="progress-bar-container" style="display: none;">
                            <div class="progress-bar-inner" id="progress-bar-inner"></div>
                            <div class="progress-bar-text" id="progress-bar-text">Creating a framework for your role</div>
                        </div>

                        <nav class="nav-container" id="navigation">
                            <button class="nav-button active" data-section="role">Role Information</button>
                            <button class="nav-button" data-section="competencies">Core Competencies</button>
                            <button class="nav-button" data-section="development">Development Path</button>
                            <button class="nav-button" data-section="metrics">Success Metrics</button>
                        </nav>


                        <!-- Content Area -->
                        <div id="content" class="section-content" style="display: none;">
                            <!-- Dynamic content will be injected here -->
                        </div>

                        <button id="proceed-to-assessment-btn" class="proceed-button" style="display: none;">
                            Proceed to Assessment
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14M12 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    async function fetchFrameworkData(role) {
        let progressInterval;
        const contentDiv = document.getElementById('content');
        const proceedButton = document.getElementById('proceed-to-assessment-btn');

        // Check if we already have the framework data in cache
        const cachedFramework = getCachedFramework(role);
        if (cachedFramework) {
            console.log('Using cached framework data for role:', role);
            frameworkData = cachedFramework;

            // Update UI with cached data
            updateUIWithFrameworkData();

            // Show proceed button
            if (proceedButton) {
                proceedButton.style.display = 'flex';
                console.log('Proceed button displayed from cached data');
            } else {
                console.error('Proceed button element not found');
            }

            return frameworkData;
        }

        try {
            // Show content with skeleton loaders
            if (contentDiv) {
                contentDiv.style.display = 'block';
                contentDiv.innerHTML = generateSkeletonLoaders();
            }

            // Show toast message
            showToast('We are creating a framework for your role. This might take a few seconds.');

            // Show progress bar
            const progressBarContainer = document.getElementById('progress-bar-container');
            const progressBarInner = document.getElementById('progress-bar-inner');

            if (progressBarContainer && progressBarInner) {
                progressBarContainer.style.display = 'block';
                progressBarInner.style.width = '0%';
                progressBarInner.style.transition = 'width 0.4s ease-in-out';
            }

            let progress = 0;
            progressInterval = setInterval(() => {
                progress += Math.random() * 12;
                if (progress > 90) progress = 90;
                if (progressBarInner) {
                    progressBarInner.style.width = progress + '%';
                }
                const progressText = document.getElementById('progress-bar-text');
                if (progressText) {
                    progressText.textContent = `Creating a framework for your role (${Math.round(progress)}%)`;
                }
            }, 800); // Slower interval of 800ms

            // Include email as session ID
            const email = document.getElementById("email")?.value.trim() || null;

            // Check if request is already pending
            const frameworkParams = { role };

            if (isRequestPending('generate-framework', frameworkParams)) {
                console.log('Framework generation already in progress. Waiting for completion...');

                // Instead of throwing an error, we'll show a more specific waiting message
                const progressText = document.getElementById('progress-bar-text');
                if (progressText) {
                    progressText.textContent = `Waiting for framework generation to complete...`;
                }

                // Keep showing the loading indicators, don't throw an error

                // Poll for the framework to become available in cache
                const maxWaitTime = 60000; // 60 seconds maximum wait time
                const pollInterval = 2000; // Check every 2 seconds
                let waitTime = 0;

                return new Promise((resolve, reject) => {
                    const checkCache = setInterval(() => {
                        const cachedFramework = getCachedFramework(role);
                        waitTime += pollInterval;

                        if (cachedFramework) {
                            // Framework is now available in cache
                            clearInterval(checkCache);
                            clearInterval(progressInterval);

                            // Quick completion to 100%
                            if (progressBarInner) {
                                progressBarInner.style.width = '100%';
                                progressBarInner.style.transition = 'width 0.3s ease-out';
                            }

                            // Update UI with cached data
                            frameworkData = cachedFramework;

                            setTimeout(() => {
                                if (progressBarContainer) {
                                    progressBarContainer.style.display = 'none';
                                }
                                updateUIWithFrameworkData();

                                // Show proceed button
                                if (proceedButton) {
                                    proceedButton.style.display = 'flex';
                                    console.log('Proceed button displayed after waiting for cached framework');
                                }
                            }, 500);

                            resolve(cachedFramework);
                        } else if (waitTime >= maxWaitTime) {
                            // Timeout reached, framework still not available
                            clearInterval(checkCache);
                            clearInterval(progressInterval);

                            const errorMsg = 'Timed out waiting for framework generation';
                            console.warn(errorMsg);

                            if (progressBarContainer) {
                                progressBarContainer.style.display = 'none';
                            }

                            reject(new Error(errorMsg));
                        } else {
                            // Update progress to show we're still waiting
                            if (progressBarInner) {
                                progress = Math.min(90, progress + 2);
                                progressBarInner.style.width = progress + '%';
                            }

                            if (progressText) {
                                progressText.textContent = `Waiting for framework (${Math.round(waitTime/1000)}s)...`;
                            }
                        }
                    }, pollInterval);
                });
            }

            // Mark request as pending
            const frameworkRequestKey = markRequestPending('generate-framework', frameworkParams);

            const response = await fetch('/api/generate-framework', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    role,
                    email
                }),
            });

            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }

            frameworkData = await response.json();

            // Cache the framework data for other components to use
            cacheFramework(role, frameworkData);

            // Clear the slow progress interval
            clearInterval(progressInterval);

            // Quick completion to 100%
            progressBarInner.style.width = '100%';
            progressBarInner.style.transition = 'width 0.3s ease-out';

            // Hide progress bar after a short delay
            setTimeout(() => {
                if (progressBarContainer) {
                    progressBarContainer.style.display = 'none';
                }
                updateUIWithFrameworkData();

                // Show proceed button
                if (proceedButton) {
                    proceedButton.style.display = 'flex';
                    console.log('Proceed button displayed after framework fetching');
                } else {
                    console.error('Proceed button element not found after fetching framework');
                }
            }, 500);

            // Mark request as completed
            markRequestCompleted(frameworkRequestKey);

            return frameworkData;
        } catch (error) {
            console.error('Error fetching framework data:', error);

            // Show error message in content area
            if (contentDiv) {
                contentDiv.innerHTML = `
                    <div class="error-message">
                        <p>Sorry, there was an error generating the framework. Please try again.</p>
                        <p class="error-details">${error.message}</p>
                    </div>
                `;
                contentDiv.style.display = 'block';
            }

            // Clear progress interval if it exists
            if (progressInterval) {
                clearInterval(progressInterval);
            }

            // Hide progress bar
            const progressBarContainer = document.getElementById('progress-bar-container');
            if (progressBarContainer) {
                progressBarContainer.style.display = 'none';
            }

            // Show toast with error
            showToast('Error loading framework. Please try again.', 5000);

            throw error;
        }
    }

    function updateUIWithFrameworkData() {
        if (!frameworkData) {
            console.error('Framework data not available for UI update');
            return;
        }

        console.log('Updating UI with framework data');
        setActiveSection('role');

        // Make sure content div is visible
        const contentDiv = document.getElementById('content');
        if (contentDiv) {
            contentDiv.style.display = 'block';
        }

        // Ensure proceed button is visible
        const proceedButton = document.getElementById('proceed-to-assessment-btn');
        if (proceedButton) {
            proceedButton.style.display = 'flex';
            console.log('Proceed button display set to flex in updateUIWithFrameworkData');
        } else {
            console.error('Proceed button not found in updateUIWithFrameworkData');
        }
    }

    function generateSkeletonLoaders() {
        return `
            <div class="skeleton-title skeleton"></div>
            <div class="skeleton-text skeleton" style="width: 90%"></div>
            <div class="skeleton-text skeleton" style="width: 85%"></div>
            <div class="skeleton-text skeleton" style="width: 80%"></div>
            <div class="skeleton-card skeleton"></div>
            <div class="skeleton-card skeleton"></div>
            <div class="skeleton-card skeleton"></div>
        `;
    }

    function setActiveSection(section) {
        // Update navigation buttons
        const buttons = document.querySelectorAll('.nav-button');
        buttons.forEach(button => {
            if (button.dataset.section === section) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });

        // Update content
        const contentDiv = document.getElementById('content');

        if (!frameworkData) {
            // Content is not available yet
            if (contentDiv) contentDiv.style.display = 'none';
        } else {
            // Content is available
            if (contentDiv) contentDiv.style.display = 'block';

            let content = '';

            switch(section) {
                case 'role':
                    content = renderRole();
                    break;
                case 'competencies':
                    content = renderCompetencies();
                    setTimeout(() => {
                        renderCompetenciesChart();
                    }, 100);
                    break;
                case 'development':
                    content = renderDevelopmentPath();
                    break;
                case 'metrics':
                    content = renderMetrics();
                    break;
            }

            contentDiv.innerHTML = content;
        }
    }

    function renderRole() {
        if (!frameworkData) {
            return '<div class="loading">Loading framework data...</div>';
        }

        return `
            <div class="section-content">
                <h2 class="text-xl font-semibold mb-4 text-green-600">${frameworkData.role.title}</h2>
                <p class="text-gray-800">${frameworkData.role.description}</p>
            </div>
        `;
    }

    function renderCompetencies() {
        if (!frameworkData) {
            return '<div class="loading">Loading competencies...</div>';
        }

        return `
            <div class="section-content">
                <h2 class="text-xl font-semibold mb-4 text-green-600">Core Competencies</h2>
                <div class="chart-container">
                    <canvas id="competenciesChart"></canvas>
                </div>
                <div class="competencies-grid">
                    ${frameworkData.coreCompetencies.map(competency => `
                        <div class="competency-card">
                            <h3 class="competency-title">${competency.title}</h3>
                            <ul class="skills-list">
                                ${competency.requiredSkills.map(skill => `
                                    <li class="text-gray-700 text-sm">${skill}</li>
                                `).join('')}
                            </ul>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function renderCompetenciesChart() {
        const canvas = document.getElementById('competenciesChart');
        if (!canvas || !frameworkData) return;

        const ctx = canvas.getContext('2d');
        const competencyData = frameworkData.coreCompetencies.map(comp => ({
            label: comp.title,
            value: comp.requiredSkills.length
        }));

        // Create gradient definitions
        const createGradient = (ctx, colorStart, colorEnd) => {
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, colorStart);
            gradient.addColorStop(1, colorEnd);
            return gradient;
        };

        // Define the gradient colors as requested
        const gradients = [
            createGradient(ctx, '#5A5CDB', '#8E5BDB'), // Purple/violet gradient
            createGradient(ctx, '#1A2B5F', '#324577'), // Navy blue gradient
            createGradient(ctx, '#00AA90', '#30D978'), // Teal/green gradient
            createGradient(ctx, '#FF8F56', '#FFB347')  // Orange/coral gradient
        ];

        // Repeat gradients if we have more data points than gradients
        const backgroundColors = competencyData.map((_, index) =>
            gradients[index % gradients.length]
        );

        // Create the chart with animation
        new Chart(ctx, {
            type: 'doughnut', // Use doughnut for gauge-style appearance
            data: {
                labels: competencyData.map(d => d.label),
                datasets: [{
                    data: competencyData.map(d => d.value),
                    backgroundColor: backgroundColors,
                    borderColor: 'rgba(255, 255, 255, 0.8)',
                    borderWidth: 1,
                    borderRadius: 10, // Rounded corners on segments
                    spacing: 2, // Small spacing between segments
                    cutout: '60%', // Doughnut hole size for gauge-style look
                    hoverOffset: 5 // Slightly expand segments on hover
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    animateRotate: true, // Enable rotation animation
                    animateScale: true,   // Enable scaling animation
                    duration: 1500,       // Animation duration in milliseconds
                    easing: 'easeOutCirc' // Smooth circular easing
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                family: "'Montserrat', sans-serif",
                                size: 12,
                                weight: '500'
                            },
                            color: '#333333',
                            padding: 15,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(18, 28, 65, 0.8)', // Semi-transparent DARK_BLUE
                        titleFont: {
                            family: "'Montserrat', sans-serif",
                            size: 14,
                            weight: '600'
                        },
                        bodyFont: {
                            family: "'Montserrat', sans-serif",
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 6,
                        displayColors: true
                    }
                }
            }
        });
    }

    function renderDevelopmentPath() {
        if (!frameworkData) {
            return '<div class="loading">Loading development path...</div>';
        }

        return `
            <div class="section-content">
                <h2 class="text-xl font-semibold mb-4 text-green-600">Development Path</h2>
                ${frameworkData.developmentPath.levels.map(level => `
                    <div class="competency-card">
                        <h3 class="competency-title">${level.name}</h3>
                        <p class="text-gray-800 mb-2">${level.focus}</p>
                        <ul class="skills-list">
                            ${level.outcomes.map(outcome => `
                                <li class="text-gray-700 text-sm">${outcome}</li>
                            `).join('')}
                        </ul>
                    </div>
                `).join('')}
            </div>
        `;
    }

    function renderMetrics() {
        if (!frameworkData) {
            return '<div class="loading">Loading metrics...</div>';
        }

        return `
            <div class="section-content">
                <h2 class="text-xl font-semibold mb-4 text-green-600">Success Metrics</h2>
                <ul class="space-y-2">
                    ${frameworkData.successMetrics.map(metric => `
                        <li class="flex items-start gap-2">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span class="text-gray-800">${metric}</span>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;
    }

    function setupEventListeners() {
        const navigation = document.getElementById('navigation');
        if (navigation) {
            navigation.addEventListener('click', function(e) {
                const button = e.target.closest('.nav-button');
                if (button && button.dataset.section) {
                    setActiveSection(button.dataset.section);
                }
            });
        }
    }

    function showError(message) {
        const contentDiv = document.getElementById('content');
        if (contentDiv) {
            contentDiv.innerHTML = `
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                    ${message}
                </div>
            `;
        }
    }

    // Expose the necessary functions to global object
    global.createSkillsGapAnalyzer = createSkillsGapAnalyzer;
    global.fetchFrameworkData = fetchFrameworkData;

})(typeof window !== 'undefined' ? window : this);
