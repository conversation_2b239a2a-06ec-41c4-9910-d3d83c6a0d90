<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #3182ce;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2c5aa0;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Validation Implementation Test</h1>
    
    <div class="test-section">
        <h2>Email Validation Test</h2>
        <div class="form-group">
            <label for="test-email">Email:</label>
            <input type="email" id="test-email" placeholder="Enter email to test">
            <div id="email-error-text" class="error-text"></div>
        </div>
        <button class="test-button" onclick="testEmailValidation()">Test Email Validation</button>
        <div id="email-result"></div>
    </div>

    <div class="test-section">
        <h2>Role Validation Test</h2>
        <div class="form-group">
            <label for="test-role">Role:</label>
            <input type="text" id="test-role" placeholder="Enter role to test">
            <div id="role-error-text" class="error-text"></div>
        </div>
        <button class="test-button" onclick="testRoleValidation()">Test Role Validation</button>
        <div id="role-result"></div>
    </div>

    <div class="test-section">
        <h2>Error Handling Test</h2>
        <button class="test-button" onclick="testEmailError()">Test Email Error Display</button>
        <button class="test-button" onclick="testRoleError()">Test Role Error Display</button>
        <button class="test-button" onclick="testErrorClearing()">Test Error Clearing</button>
    </div>

    <div class="test-section">
        <h2>Validation Overlay Test</h2>
        <button class="test-button" onclick="testValidationOverlay()">Show Validation Overlay</button>
        <button class="test-button" onclick="hideValidationLoadingOverlay()">Hide Validation Overlay</button>
    </div>

    <!-- Modern Validation Overlay -->
    <div id="validation-overlay" class="validation-overlay">
      <div class="validation-container">
        <div class="validation-spinner-container">
          <div class="validation-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
        </div>
        <div class="validation-content">
          <h3 class="validation-title">Validating Information</h3>
          <p class="validation-subtitle">Please wait while we verify your details...</p>
        </div>
      </div>
    </div>

    <script src="validationFunctions.js"></script>
    <script>
        // Mock Firebase and global variables for testing
        window.db = {
            collectionGroup: () => ({
                where: () => ({
                    get: () => Promise.resolve({ empty: true })
                })
            })
        };
        window.userCompany = "";
        window.commonRoles = ["CEO", "Manager", "Developer", "Designer"];

        // Mock global validation state variables for legacy functions
        window.isEmailValid = false;
        window.isRoleValid = false;
        window.updateSubmitButton = function() {
            console.log('Submit button update called');
        };

        async function testEmailValidation() {
            const email = document.getElementById('test-email').value;
            const resultDiv = document.getElementById('email-result');
            
            try {
                const result = await validateEmailOnSubmit(email);
                resultDiv.innerHTML = `<div class="${result.isValid ? 'success' : 'error'} result">
                    <strong>Result:</strong> ${result.isValid ? 'Valid' : 'Invalid'}<br>
                    ${result.error ? `<strong>Error:</strong> ${result.error}` : ''}
                    ${result.errorType ? `<br><strong>Error Type:</strong> ${result.errorType}` : ''}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error result">Error: ${error.message}</div>`;
            }
        }

        async function testRoleValidation() {
            const role = document.getElementById('test-role').value;
            const resultDiv = document.getElementById('role-result');
            
            try {
                const result = await validateRoleOnSubmit(role);
                resultDiv.innerHTML = `<div class="${result.isValid ? 'success' : 'error'} result">
                    <strong>Result:</strong> ${result.isValid ? 'Valid' : 'Invalid'}<br>
                    ${result.error ? `<strong>Error:</strong> ${result.error}` : ''}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error result">Error: ${error.message}</div>`;
            }
        }

        function testEmailError() {
            showEmailValidationError('This is a test email error message');
        }

        function testRoleError() {
            showRoleValidationError('This is a test role error message');
        }

        function testErrorClearing() {
            // First show errors
            showEmailValidationError('Test email error');
            showRoleValidationError('Test role error');
            
            // Then clear them after 2 seconds
            setTimeout(() => {
                clearEmailValidationError();
                clearRoleValidationError();
            }, 2000);
        }

        function testValidationOverlay() {
            showValidationLoadingOverlay();
            // Auto-hide after 3 seconds
            setTimeout(() => {
                hideValidationLoadingOverlay();
            }, 3000);
        }

        // Add error clearing listeners
        document.getElementById('test-email').addEventListener('input', () => {
            clearEmailValidationError();
        });

        document.getElementById('test-role').addEventListener('input', () => {
            clearRoleValidationError();
        });
    </script>
</body>
</html>
