(function(global) {

    // Submission-based email validation function
    async function validateEmailOnSubmit(email) {
        try {
            // Basic email format validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return {
                    isValid: false,
                    error: 'Please enter a valid email address',
                    errorType: 'format'
                };
            }

            // Check invitation status
            const emailResponse = await db.collectionGroup('inviteeSummary')
                .where('sentEmails', 'array-contains', email)
                .get();

            if (!emailResponse.empty) {
                const companySnapshot = await db.collection('companies').get();

                for (const companyDoc of companySnapshot.docs) {
                    const inviteeSummary = await companyDoc.ref.collection('inviteeSummary')
                        .doc('summary')
                        .get();

                    if (inviteeSummary.exists &&
                        inviteeSummary.data().sentEmails &&
                        inviteeSummary.data().sentEmails.includes(email)) {
                        userCompany = companyDoc.id;
                        console.log('Set userCompany to:', userCompany);
                        return { isValid: true, company: userCompany };
                    }
                }
            }

            // Email not found in invitations
            userCompany = "";
            console.log('Reset userCompany - email not invited');
            return {
                isValid: false,
                error: 'This email address has not been invited to take this assessment',
                errorType: 'invitation'
            };
        } catch (error) {
            console.error('Error validating email:', error);
            userCompany = "";
            return {
                isValid: false,
                error: 'An error occurred while validating your email. Please try again.',
                errorType: 'system'
            };
        }
    }

    // Submission-based role validation function
    async function validateRoleOnSubmit(role) {
        const normalizedRole = role.toLowerCase().trim();

        // Quick check against common roles
        if (typeof commonRoles !== 'undefined') {
            const commonRolesSet = new Set(commonRoles.map(r => r.toLowerCase()));
            if (commonRolesSet.has(normalizedRole)) {
                return { isValid: true };
            }
        }

        // API validation for uncommon roles
        try {
            const response = await fetch('/api/validate-role', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ role })
            });

            const result = await response.json();
            return {
                isValid: result.isValid,
                error: result.isValid ? undefined : 'Please enter a valid job role that represents a genuine occupation'
            };
        } catch (error) {
            console.error('Error validating role:', error);
            return {
                isValid: false,
                error: 'An error occurred while validating your role. Please try again.'
            };
        }
    }

    // Error handling functions
    function showEmailValidationError(message) {
        const emailInput = document.getElementById('email');
        const errorText = document.getElementById('email-error-text');

        if (emailInput) {
            emailInput.classList.add('input-error', 'shake');
            emailInput.focus();
            setTimeout(() => emailInput.classList.remove('shake'), 600);
        }

        if (errorText) {
            errorText.textContent = message;
            errorText.classList.add('show');
        }
    }

    function showRoleValidationError(message) {
        const roleInput = document.getElementById('role');
        const errorText = document.getElementById('role-error-text');

        if (roleInput) {
            roleInput.classList.add('input-error', 'shake');
            roleInput.focus();
            setTimeout(() => roleInput.classList.remove('shake'), 600);
        }

        if (errorText) {
            errorText.textContent = message;
            errorText.classList.add('show');
        }
    }

    function clearEmailValidationError() {
        const emailInput = document.getElementById('email');
        const errorText = document.getElementById('email-error-text');

        if (emailInput) {
            emailInput.classList.remove('input-error', 'shake');
        }

        if (errorText) {
            errorText.classList.remove('show');
            errorText.textContent = '';
        }
    }

    function clearRoleValidationError() {
        const roleInput = document.getElementById('role');
        const errorText = document.getElementById('role-error-text');

        if (roleInput) {
            roleInput.classList.remove('input-error', 'shake');
        }

        if (errorText) {
            errorText.classList.remove('show');
            errorText.textContent = '';
        }
    }

    // Validation overlay functions
    function showValidationLoadingOverlay() {
        const overlay = document.getElementById('validation-overlay');
        if (overlay) {
            overlay.style.display = 'flex';
            setTimeout(() => {
                overlay.style.opacity = '1';
                overlay.querySelector('.validation-container').style.transform = 'scale(1)';
            }, 10);
        }
    }

    function hideValidationLoadingOverlay() {
        const overlay = document.getElementById('validation-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            overlay.querySelector('.validation-container').style.transform = 'scale(0.9)';
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 300);
        }
    }

    // Legacy email validation function for backward compatibility
    async function validateEmail(email) {
        try {
          const emailResponse = await db.collectionGroup('inviteeSummary')
            .where('sentEmails', 'array-contains', email)
            .get();
      
          if (!emailResponse.empty) {
            const companySnapshot = await db.collection('companies').get();
            
            for (const companyDoc of companySnapshot.docs) {
              const inviteeSummary = await companyDoc.ref.collection('inviteeSummary')
                .doc('summary')
                .get();
              
              if (inviteeSummary.exists && 
                  inviteeSummary.data().sentEmails && 
                  inviteeSummary.data().sentEmails.includes(email)) {
                userCompany = companyDoc.id;
                console.log('Set userCompany to:', userCompany); // Add logging
                
                // Validation icon removed
                
                const emailInput = document.getElementById("email");
                emailInput.classList.remove('border-red-500');
                emailInput.classList.add('border-green-500');
                isEmailValid = true;
                updateSubmitButton();
                return true;
              }
            }
          }
      
          userCompany = "";
          console.log('Reset userCompany to empty string'); // Add logging
          
          // Validation icon removed
          
          const emailInput = document.getElementById("email");
          emailInput.classList.remove('border-green-500');
          emailInput.classList.add('border-red-500');
          isEmailValid = false;
          updateSubmitButton();
          return false;
        } catch (error) {
          console.error('Error validating email:', error);
          userCompany = ""; // Reset on error
          console.log('Reset userCompany due to error'); // Add logging
          return false;
        }
      }

      // Role validation function
async function validateRole(role) {
    const normalizedRole = role.toLowerCase().trim();
    const roleInput = document.getElementById('role');
    // Validation icon removed
  
    // First check if it's in the common roles
    if (commonRolesSet.has(normalizedRole)) {
      showRoleValidationSuccess();
      isRoleValid = true;
      updateSubmitButton();
      return true;
    }
  
    // If not in common roles, validate via API
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(async () => {
      try {
        const response = await fetch('/api/validate-role', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ role })
        });
  
        const result = await response.json();
        
        if (result.isValid) {
          showRoleValidationSuccess();
          isRoleValid = true;
        } else {
          // Validation icon removed
          roleInput.classList.remove('border-green-500');
          roleInput.classList.add('border-red-500');
          isRoleValid = false;
          
          // Show notification with debounce
          if (roleDebounceTimer) {
            clearTimeout(roleDebounceTimer);
          }
          roleDebounceTimer = setTimeout(() => {
            showNotification('Please input a valid job role', 'error');
          }, DEBOUNCE_DELAY);
        }
        updateSubmitButton();
      } catch (error) {
        console.error('Error validating role:', error);
        // Validation icon removed
        roleInput.classList.remove('border-green-500');
        roleInput.classList.add('border-red-500');
        isRoleValid = false;
        updateSubmitButton();
  
        if (roleDebounceTimer) {
          clearTimeout(roleDebounceTimer);
        }
        roleDebounceTimer = setTimeout(() => {
          showNotification('An error occurred while validating the role. Please try again.', 'error');
        }, DEBOUNCE_DELAY);
      }
    }, 500);
  }

  function showRoleValidationError(debounced = false) {
    // Validation icon removed
    const roleInput = document.getElementById('role');
    roleInput.classList.remove('border-green-500');
    roleInput.classList.add('border-red-500');
    
    if (debounced) {
      if (roleDebounceTimer) {
        clearTimeout(roleDebounceTimer);
      }
      roleDebounceTimer = setTimeout(() => {
        showNotification('Please input a valid job role', 'error');
      }, DEBOUNCE_DELAY);
    }
  }

  function removeErrorNotification() {
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => {
      if (notification.textContent.includes('Please input a valid job role')) {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
      }
    });
  }

  
function resetRoleValidation() {
    // Validation icon removed
    const roleInput = document.getElementById('role');
    roleInput.classList.remove('border-red-500', 'border-green-500');
    removeErrorNotification();
  }

  function showRoleValidationSuccess() {
    // Validation icon removed
    const roleInput = document.getElementById('role');
    roleInput.classList.remove('border-red-500');
    roleInput.classList.add('border-green-500');
  }

  function resetValidationState() {
    const roleInput = document.getElementById('role');
    // Validation icon removed

    isEmailValid = false;
    isRoleValid = false;

    roleInput.classList.remove('border-red-500', 'border-green-500');

    updateSubmitButton();
  }

  function showValidationLoading(inputId) {
    // Validation icon removed - function kept for backward compatibility
  }
  
   
    // Export new submission-based validation functions
    global.validateEmailOnSubmit = validateEmailOnSubmit;
    global.validateRoleOnSubmit = validateRoleOnSubmit;
    global.showEmailValidationError = showEmailValidationError;
    global.showRoleValidationError = showRoleValidationError;
    global.clearEmailValidationError = clearEmailValidationError;
    global.clearRoleValidationError = clearRoleValidationError;
    global.showValidationLoadingOverlay = showValidationLoadingOverlay;
    global.hideValidationLoadingOverlay = hideValidationLoadingOverlay;

    // Export legacy functions for backward compatibility
    global.validateEmail = validateEmail;
    global.validateRole = validateRole;
    global.removeErrorNotification = removeErrorNotification;
    global.resetRoleValidation = resetRoleValidation;
    global.showRoleValidationSuccess = showRoleValidationSuccess;
    global.resetValidationState = resetValidationState;
    global.showValidationLoading = showValidationLoading;
  })(typeof window !== 'undefined' ? window : global);