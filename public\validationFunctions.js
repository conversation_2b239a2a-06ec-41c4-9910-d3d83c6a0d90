(function(global) {

    async function validateEmail(email) {
        try {
          const emailResponse = await db.collectionGroup('inviteeSummary')
            .where('sentEmails', 'array-contains', email)
            .get();
      
          if (!emailResponse.empty) {
            const companySnapshot = await db.collection('companies').get();
            
            for (const companyDoc of companySnapshot.docs) {
              const inviteeSummary = await companyDoc.ref.collection('inviteeSummary')
                .doc('summary')
                .get();
              
              if (inviteeSummary.exists && 
                  inviteeSummary.data().sentEmails && 
                  inviteeSummary.data().sentEmails.includes(email)) {
                userCompany = companyDoc.id;
                console.log('Set userCompany to:', userCompany); // Add logging
                
                const validationIcon = document.getElementById("email-validation-icon");
                if (validationIcon) {
                  validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
                }
                
                const emailInput = document.getElementById("email");
                emailInput.classList.remove('border-red-500');
                emailInput.classList.add('border-green-500');
                isEmailValid = true;
                updateSubmitButton();
                return true;
              }
            }
          }
      
          userCompany = "";
          console.log('Reset userCompany to empty string'); // Add logging
          
          const validationIcon = document.getElementById("email-validation-icon");
          if (validationIcon) {
            validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
          }
          
          const emailInput = document.getElementById("email");
          emailInput.classList.remove('border-green-500');
          emailInput.classList.add('border-red-500');
          isEmailValid = false;
          updateSubmitButton();
          return false;
        } catch (error) {
          console.error('Error validating email:', error);
          userCompany = ""; // Reset on error
          console.log('Reset userCompany due to error'); // Add logging
          return false;
        }
      }

      // Submission-based email validation function
      async function validateEmailOnSubmit(email) {
          try {
              // Basic email format validation
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
              if (!emailRegex.test(email)) {
                  return {
                      isValid: false,
                      error: 'Please enter a valid email address',
                      errorType: 'format'
                  };
              }

              // Check if email is in invitation list
              const emailResponse = await db.collectionGroup('inviteeSummary')
                  .where('sentEmails', 'array-contains', email)
                  .get();

              if (!emailResponse.empty) {
                  const companySnapshot = await db.collection('companies').get();

                  for (const companyDoc of companySnapshot.docs) {
                      const inviteeSummary = await companyDoc.ref.collection('inviteeSummary')
                          .doc('summary')
                          .get();

                      if (inviteeSummary.exists &&
                          inviteeSummary.data().sentEmails &&
                          inviteeSummary.data().sentEmails.includes(email)) {
                          return {
                              isValid: true,
                              company: companyDoc.id
                          };
                      }
                  }
              }

              return {
                  isValid: false,
                  error: 'This email address has not been invited to take this assessment',
                  errorType: 'invitation'
              };
          } catch (error) {
              console.error('Error validating email:', error);
              return {
                  isValid: false,
                  error: 'An error occurred while validating your email. Please try again.',
                  errorType: 'system'
              };
          }
      }

      // Role validation function
async function validateRole(role) {
    const normalizedRole = role.toLowerCase().trim();
    const roleInput = document.getElementById('role');
    const validationIcon = document.getElementById('role-validation-icon');

    // First check if it's in the common roles
    if (typeof commonRoles !== 'undefined') {
      const commonRolesSet = new Set(commonRoles.map(r => r.toLowerCase()));
      if (commonRolesSet.has(normalizedRole)) {
        showRoleValidationSuccess();
        isRoleValid = true;
        updateSubmitButton();
        return true;
      }
    }
  
    // If not in common roles, validate via API
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(async () => {
      try {
        const response = await fetch('/api/validate-role', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ role })
        });
  
        const result = await response.json();
        
        if (result.isValid) {
          showRoleValidationSuccess();
          isRoleValid = true;
        } else {
          // Show X icon for invalid role
          validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
          roleInput.classList.remove('border-green-500');
          roleInput.classList.add('border-red-500');
          isRoleValid = false;
          
          // Show notification with debounce
          if (roleDebounceTimer) {
            clearTimeout(roleDebounceTimer);
          }
          roleDebounceTimer = setTimeout(() => {
            showNotification('Please input a valid job role', 'error');
          }, DEBOUNCE_DELAY);
        }
        updateSubmitButton();
      } catch (error) {
        console.error('Error validating role:', error);
        // Show X icon for error state
        validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
        roleInput.classList.remove('border-green-500');
        roleInput.classList.add('border-red-500');
        isRoleValid = false;
        updateSubmitButton();
  
        if (roleDebounceTimer) {
          clearTimeout(roleDebounceTimer);
        }
        roleDebounceTimer = setTimeout(() => {
          showNotification('An error occurred while validating the role. Please try again.', 'error');
        }, DEBOUNCE_DELAY);
      }
    }, 500);
  }

  // Submission-based role validation function
  async function validateRoleOnSubmit(role) {
      const normalizedRole = role.toLowerCase().trim();

      // Quick check against common roles
      if (typeof commonRoles !== 'undefined') {
          const commonRolesSet = new Set(commonRoles.map(r => r.toLowerCase()));
          if (commonRolesSet.has(normalizedRole)) {
              return { isValid: true };
          }
      }

      // API validation for uncommon roles
      try {
          const response = await fetch('/api/validate-role', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ role })
          });

          const result = await response.json();

          if (result.isValid) {
              return { isValid: true };
          } else {
              return {
                  isValid: false,
                  error: 'Please enter a valid job role that represents a genuine occupation'
              };
          }
      } catch (error) {
          console.error('Error validating role:', error);
          return {
              isValid: false,
              error: 'An error occurred while validating the role. Please try again.'
          };
      }
  }

  function showRoleValidationError(debounced = false) {
    const validationIcon = document.getElementById('role-validation-icon');
    validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
    roleInput.classList.remove('border-green-500');
    roleInput.classList.add('border-red-500');
    
    if (debounced) {
      if (roleDebounceTimer) {
        clearTimeout(roleDebounceTimer);
      }
      roleDebounceTimer = setTimeout(() => {
        showNotification('Please input a valid job role', 'error');
      }, DEBOUNCE_DELAY);
    }
  }

  function removeErrorNotification() {
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => {
      if (notification.textContent.includes('Please input a valid job role')) {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
      }
    });
  }

  
function resetRoleValidation() {
    const validationIcon = document.getElementById('role-validation-icon');
    if (validationIcon) {
      validationIcon.innerHTML = '';
    }
    roleInput.classList.remove('border-red-500', 'border-green-500');
    removeErrorNotification();
  }

  function showRoleValidationSuccess() {
    const validationIcon = document.getElementById('role-validation-icon');
    const roleInput = document.getElementById('role');
    validationIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
    roleInput.classList.remove('border-red-500');
    roleInput.classList.add('border-green-500');
  }

  function resetValidationState() {
    const roleInput = document.getElementById('role');
    const roleValidationIcon = document.getElementById('role-validation-icon');
    
    isEmailValid = false;
    isRoleValid = false;
    
    if (roleValidationIcon) {
      roleValidationIcon.innerHTML = '';
    }
    roleInput.classList.remove('border-red-500', 'border-green-500');
    
    updateSubmitButton();
  }

  function showValidationLoading(inputId) {
    const validationIcon = document.getElementById(`${inputId}-validation-icon`);
    if (validationIcon) {
      validationIcon.innerHTML = '<div class="validation-spinner"></div>';
    }
  }

  // Enhanced error display functions with animations
  function showEmailValidationError(message) {
      const emailInput = document.getElementById('email');
      const errorText = document.getElementById('email-error-text');

      if (emailInput) {
          emailInput.classList.add('input-error', 'shake');
          emailInput.focus();
          setTimeout(() => emailInput.classList.remove('shake'), 600);
      }

      if (errorText) {
          errorText.textContent = message;
          errorText.classList.add('show');
      }
  }

  function showRoleValidationError(message) {
      const roleInput = document.getElementById('role');
      const errorText = document.getElementById('role-error-text');

      if (roleInput) {
          roleInput.classList.add('input-error', 'shake');
          roleInput.focus();
          setTimeout(() => roleInput.classList.remove('shake'), 600);
      }

      if (errorText) {
          errorText.textContent = message;
          errorText.classList.add('show');
      }
  }

  function clearEmailValidationError() {
      const emailInput = document.getElementById('email');
      const errorText = document.getElementById('email-error-text');

      if (emailInput) {
          emailInput.classList.remove('input-error', 'shake');
      }

      if (errorText) {
          errorText.classList.remove('show');
          errorText.textContent = '';
      }
  }

  function clearRoleValidationError() {
      const roleInput = document.getElementById('role');
      const errorText = document.getElementById('role-error-text');

      if (roleInput) {
          roleInput.classList.remove('input-error', 'shake');
      }

      if (errorText) {
          errorText.classList.remove('show');
          errorText.textContent = '';
      }
  }

  // Modern validation loading overlay functions
  function showValidationLoadingOverlay() {
      const validationOverlay = document.getElementById("validation-overlay");
      if (!validationOverlay) return;

      validationOverlay.style.display = "flex";

      // Use requestAnimationFrame to ensure the display change is applied before adding the show class
      requestAnimationFrame(() => {
          validationOverlay.classList.add("show");
      });
  }

  function hideValidationLoadingOverlay() {
      const validationOverlay = document.getElementById("validation-overlay");
      if (!validationOverlay) return;

      validationOverlay.classList.remove("show");

      // Wait for the transition to complete before hiding
      setTimeout(() => {
          validationOverlay.style.display = "none";
      }, 300);
  }


    global.validateEmail = validateEmail;
    global.validateEmailOnSubmit = validateEmailOnSubmit;
    global.validateRole = validateRole;
    global.validateRoleOnSubmit = validateRoleOnSubmit;
    global.showEmailValidationError = showEmailValidationError;
    global.showRoleValidationError = showRoleValidationError;
    global.clearEmailValidationError = clearEmailValidationError;
    global.clearRoleValidationError = clearRoleValidationError;
    global.showValidationLoadingOverlay = showValidationLoadingOverlay;
    global.hideValidationLoadingOverlay = hideValidationLoadingOverlay;
    global.removeErrorNotification = removeErrorNotification;
    global.resetRoleValidation = resetRoleValidation;
    global.showRoleValidationSuccess = showRoleValidationSuccess;
    global.resetValidationState = resetValidationState;
    global.showValidationLoading = showValidationLoading;
  })(typeof window !== 'undefined' ? window : global);