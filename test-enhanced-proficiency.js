// Test script for enhanced proficiency calculation

/**
 * Calculate proficiency percentages based on the AI's analysis of strengths and gaps
 * and self-assessment responses
 * @param {Object} analysisData - The analysis data returned by the AI
 * @returns {Object} - The updated analysis data with accurate proficiency percentages
 */
function calculateProficiencyFromStrengthsAndGaps(analysisData) {
  if (!analysisData?.report?.competencyAnalysis) {
    console.log('No competency analysis found in AI response');
    return analysisData;
  }

  // Store original data for reference
  const originalData = analysisData.originalData || {};
  const selfAssessmentData = originalData.selfAssessment || {};
  const selfAssessmentResponses = selfAssessmentData.responses || [];
  const skillLevels = selfAssessmentData.summary?.skillLevels || {};

  const analysis = analysisData.report.competencyAnalysis;

  // Process each competency area
  Object.keys(analysis).forEach(competencyName => {
    const area = analysis[competencyName];

    // Ensure arrays exist
    if (!area.strengthAreas) area.strengthAreas = [];
    if (!area.gapAreas) area.gapAreas = [];

    // Calculate percentage based on strengths vs. total (strengths + gaps)
    const strengths = area.strengthAreas.length;
    const gaps = area.gapAreas.length;
    const total = strengths + gaps;

    let strengthsGapsPercentage;
    if (total > 0) {
      // Calculate percentage of strengths
      strengthsGapsPercentage = Math.round((strengths / total) * 100);
      console.log(`Strengths/gaps ratio for ${competencyName}: ${strengthsGapsPercentage}% (${strengths}/${total})`);
    } else {
      // If no strengths or gaps identified, use a default value
      strengthsGapsPercentage = 50;
      console.log(`No strengths or gaps identified for ${competencyName}, using default: 50%`);
    }

    // Look for self-assessment data for this skill area
    const skillAreaData = skillLevels[competencyName];
    let selfAssessmentPercentage = null;

    if (skillAreaData && skillAreaData.averageLevel) {
      // Convert average level (1-3) to percentage (0-100%)
      selfAssessmentPercentage = Math.round(((skillAreaData.averageLevel - 1) / 2) * 100);
      console.log(`Self-assessment score for ${competencyName}: ${selfAssessmentPercentage}% (avg level: ${skillAreaData.averageLevel})`);
    } else {
      // Try to find responses for this skill area
      const areaResponses = selfAssessmentResponses.filter(r =>
        r.skillArea?.toLowerCase() === competencyName.toLowerCase());

      if (areaResponses.length > 0) {
        // Calculate average skill level
        const totalLevel = areaResponses.reduce((sum, r) => sum + (r.skillLevel || 1), 0);
        const avgLevel = totalLevel / areaResponses.length;
        selfAssessmentPercentage = Math.round(((avgLevel - 1) / 2) * 100);
        console.log(`Calculated self-assessment for ${competencyName}: ${selfAssessmentPercentage}% (avg level: ${avgLevel})`);
      }
    }

    // Combine both scores with appropriate weighting
    // If both scores exist, use 60% strengths/gaps and 40% self-assessment
    // If only one exists, use 100% of that one
    let finalPercentage;
    if (strengthsGapsPercentage !== null && selfAssessmentPercentage !== null) {
      finalPercentage = Math.round((strengthsGapsPercentage * 0.6) + (selfAssessmentPercentage * 0.4));
      console.log(`Combined score for ${competencyName}: ${finalPercentage}% (60% of ${strengthsGapsPercentage}% + 40% of ${selfAssessmentPercentage}%)`);
    } else if (strengthsGapsPercentage !== null) {
      finalPercentage = strengthsGapsPercentage;
    } else if (selfAssessmentPercentage !== null) {
      finalPercentage = selfAssessmentPercentage;
    } else {
      finalPercentage = 50; // Default if no data available
    }

    // Update the proficiency level
    area.proficiencyLevel = `${finalPercentage}%`;
  });

  return analysisData;
}

// Create a mock analysis data with different scenarios
const mockAnalysisData = {
  report: {
    competencyAnalysis: {
      // Communication: 2 strengths, 2 gaps = 50% strengths/gaps, 67% self-assessment
      "Communication": {
        proficiencyLevel: "67%", // This will be corrected
        strengthAreas: [
          "Communication - active listening",
          "Communication - providing clear feedback"
        ],
        gapAreas: [
          "Communication - handling difficult conversations",
          "Communication - persuasive speaking"
        ],
        progressionPath: ["Communication - Essentials", "Communication - Intermediate", "Communication - Advanced"]
      },

      // Leadership: 1 strength, 2 gaps = 33% strengths/gaps, 50% self-assessment
      "Leadership": {
        proficiencyLevel: "67%", // This will be corrected
        strengthAreas: [
          "Leadership - delegating basic tasks"
        ],
        gapAreas: [
          "Leadership - emotional intelligence",
          "Leadership - strategic vision"
        ],
        progressionPath: ["Leadership - Essentials", "Leadership - Intermediate", "Leadership - Advanced"]
      },

      // Teamwork: 3 strengths, 1 gap = 75% strengths/gaps, 33% self-assessment
      "Teamwork": {
        proficiencyLevel: "67%", // This will be corrected
        strengthAreas: [
          "Teamwork - collaboration",
          "Teamwork - sharing ideas",
          "Teamwork - supporting colleagues"
        ],
        gapAreas: [
          "Teamwork - conflict resolution"
        ],
        progressionPath: ["Teamwork - Essentials", "Teamwork - Intermediate", "Teamwork - Advanced"]
      },

      // Problem Solving: No strengths or gaps = default 50% strengths/gaps, 100% self-assessment
      "Problem Solving": {
        proficiencyLevel: "67%", // This will be corrected
        strengthAreas: [],
        gapAreas: [],
        progressionPath: ["Problem Solving - Essentials", "Problem Solving - Intermediate", "Problem Solving - Advanced"]
      },

      // Customer Service: Only self-assessment data, no strengths/gaps
      "Customer Service": {
        proficiencyLevel: "67%", // This will be corrected
        strengthAreas: [],
        gapAreas: [],
        progressionPath: ["Customer Service - Essentials", "Customer Service - Intermediate", "Customer Service - Advanced"]
      }
    },
    summary: "User has strengths in communication, leadership, and teamwork."
  },
  originalData: {
    selfAssessment: {
      responses: [
        {
          question: "How do you handle difficult conversations?",
          selectedAnswer: "I can handle most difficult conversations with confidence",
          skillLevel: 2, // Intermediate level
          skillArea: "Communication"
        },
        {
          question: "How do you provide feedback to team members?",
          selectedAnswer: "I provide clear, constructive feedback regularly",
          skillLevel: 3, // Advanced level
          skillArea: "Communication"
        },
        {
          question: "How do you delegate tasks to team members?",
          selectedAnswer: "I delegate basic tasks but struggle with complex delegation",
          skillLevel: 2, // Intermediate level
          skillArea: "Leadership"
        },
        {
          question: "How do you collaborate in a team?",
          selectedAnswer: "I contribute when asked but rarely take initiative",
          skillLevel: 1, // Basic level
          skillArea: "Teamwork"
        },
        {
          question: "How do you solve complex problems?",
          selectedAnswer: "I can solve complex problems independently and teach others",
          skillLevel: 3, // Advanced level
          skillArea: "Problem Solving"
        },
        {
          question: "How do you handle customer complaints?",
          selectedAnswer: "I can handle most customer complaints effectively",
          skillLevel: 2, // Intermediate level
          skillArea: "Customer Service"
        }
      ],
      summary: {
        skillLevels: {
          "Communication": {
            averageLevel: 2.5, // (2+3)/2 = 2.5
            category: "Advanced",
            responses: 2
          },
          "Leadership": {
            averageLevel: 2.0,
            category: "Intermediate",
            responses: 1
          },
          "Teamwork": {
            averageLevel: 1.0,
            category: "Basic",
            responses: 1
          },
          "Problem Solving": {
            averageLevel: 3.0,
            category: "Advanced",
            responses: 1
          },
          "Customer Service": {
            averageLevel: 2.0,
            category: "Intermediate",
            responses: 1
          }
        }
      }
    }
  }
};

// Test the function
console.log("=== Testing Enhanced Proficiency Calculation ===");
const updatedAnalysisData = calculateProficiencyFromStrengthsAndGaps(mockAnalysisData);

// Display the results
console.log("\n=== Final Proficiency Scores ===");
Object.entries(updatedAnalysisData.report.competencyAnalysis).forEach(([competencyName, data]) => {
  console.log(`${competencyName}: ${data.proficiencyLevel}`);
});

// Expected results
const expectedScores = {
  // 60% of 50% (strengths/gaps) + 40% of 75% (self-assessment) = 30% + 30% = 60%
  "Communication": "60%",

  // 60% of 33% (strengths/gaps) + 40% of 50% (self-assessment) = 20% + 20% = 40%
  "Leadership": "40%",

  // 60% of 75% (strengths/gaps) + 40% of 0% (self-assessment) = 45% + 0% = 45%
  "Teamwork": "45%",

  // 60% of 50% (default strengths/gaps) + 40% of 100% (self-assessment) = 30% + 40% = 70%
  "Problem Solving": "70%",

  // 60% of 50% (default strengths/gaps) + 40% of 50% (self-assessment) = 30% + 20% = 50%
  "Customer Service": "50%"
};

// Verify the results
console.log("\n=== Verification ===");
let allCorrect = true;
Object.entries(expectedScores).forEach(([competencyName, expectedScore]) => {
  const actualScore = updatedAnalysisData.report.competencyAnalysis[competencyName].proficiencyLevel;
  const isCorrect = actualScore === expectedScore;
  console.log(`${competencyName}: Expected ${expectedScore}, Got ${actualScore} - ${isCorrect ? "✓" : "✗"}`);
  if (!isCorrect) allCorrect = false;
});

if (allCorrect) {
  console.log("\n✅ All proficiency scores calculated correctly!");
} else {
  console.log("\n❌ Some proficiency scores did not match expected values.");
}
