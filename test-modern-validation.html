<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Validation Overlay Test</title>
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .demo-form {
            margin: 30px 0;
            text-align: left;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
    </style>
    
    <!-- Include the validation overlay CSS -->
    <link rel="stylesheet" href="public/style.css">
</head>
<body>
    <div class="test-container">
        <h1>🚀 Modern Validation Overlay Demo</h1>
        
        <p>This demonstrates the new modern validation overlay with spinning animation that replaces the old tick/cross icons.</p>
        
        <button class="test-button" onclick="showValidationDemo()">
            Show Validation Overlay
        </button>
        
        <button class="test-button" onclick="hideValidationDemo()">
            Hide Validation Overlay
        </button>
        
        <div class="demo-form">
            <h3>Try the Form (No Icons!)</h3>
            <div class="form-group">
                <label for="demo-email">Email</label>
                <input type="email" id="demo-email" placeholder="Enter your email">
                <div id="email-error-text" class="error-text"></div>
            </div>
            
            <div class="form-group">
                <label for="demo-role">Role</label>
                <input type="text" id="demo-role" placeholder="Enter your role">
                <div id="role-error-text" class="error-text"></div>
            </div>
            
            <button class="test-button" onclick="testValidationFlow()">
                Test Validation Flow
            </button>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h4>✨ Updated Features:</h4>
            <ul style="text-align: left; margin: 0;">
                <li>❌ <strong>Removed</strong>: Tick/cross validation icons</li>
                <li>✅ <strong>Added</strong>: Modern spinning validation overlay</li>
                <li>✅ <strong>Updated</strong>: Clean white background with blue accents</li>
                <li>✅ <strong>Added</strong>: Professional blur backdrop effect</li>
                <li>✅ <strong>Added</strong>: Smooth animations and transitions</li>
                <li>✅ <strong>Theme</strong>: Matches application's color scheme</li>
            </ul>
        </div>
    </div>

    <!-- Modern Validation Overlay -->
    <div id="validation-overlay" class="validation-overlay">
        <div class="validation-container">
            <div class="validation-spinner-container">
                <div class="validation-spinner">
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                </div>
            </div>
            <div class="validation-content">
                <h3 class="validation-title">Validating Information</h3>
                <p class="validation-subtitle">Please wait while we verify your details...</p>
            </div>
        </div>
    </div>

    <script>
        // Mock validation functions for demo
        function showValidationDemo() {
            const overlay = document.getElementById('validation-overlay');
            overlay.style.display = 'flex';
            requestAnimationFrame(() => {
                overlay.classList.add('show');
            });
        }
        
        function hideValidationDemo() {
            const overlay = document.getElementById('validation-overlay');
            overlay.classList.remove('show');
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 300);
        }
        
        function testValidationFlow() {
            // Show validation overlay
            showValidationDemo();
            
            // Simulate validation process
            setTimeout(() => {
                hideValidationDemo();
                
                // Show error messages
                setTimeout(() => {
                    const emailError = document.getElementById('email-error-text');
                    const roleError = document.getElementById('role-error-text');
                    
                    emailError.textContent = 'This email address has not been invited to take this assessment';
                    emailError.classList.add('show');
                    
                    roleError.textContent = 'Please enter a valid job role that represents a genuine occupation';
                    roleError.classList.add('show');
                    
                    // Add shake effect to inputs
                    const emailInput = document.getElementById('demo-email');
                    const roleInput = document.getElementById('demo-role');
                    
                    emailInput.classList.add('input-error', 'shake');
                    roleInput.classList.add('input-error', 'shake');
                    
                    setTimeout(() => {
                        emailInput.classList.remove('shake');
                        roleInput.classList.remove('shake');
                    }, 600);
                    
                }, 200);
            }, 2000);
        }
        
        // Clear errors when typing
        document.getElementById('demo-email').addEventListener('input', () => {
            const errorText = document.getElementById('email-error-text');
            const input = document.getElementById('demo-email');
            errorText.classList.remove('show');
            input.classList.remove('input-error');
        });
        
        document.getElementById('demo-role').addEventListener('input', () => {
            const errorText = document.getElementById('role-error-text');
            const input = document.getElementById('demo-role');
            errorText.classList.remove('show');
            input.classList.remove('input-error');
        });
    </script>
</body>
</html>
