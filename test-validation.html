<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Validation Implementation Test</h1>
    
    <div class="test-section">
        <h2>Email Validation Tests</h2>
        <button onclick="testEmailFormat()">Test Email Format Validation</button>
        <button onclick="testEmailInvitation()">Test Email Invitation Validation</button>
        <div id="email-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Role Validation Tests</h2>
        <button onclick="testRoleValidation()">Test Role Validation</button>
        <div id="role-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Error Display Tests</h2>
        <button onclick="testErrorDisplay()">Test Error Display Functions</button>
        <div id="error-test-results"></div>
        
        <!-- Test form elements -->
        <div style="margin-top: 20px;">
            <input type="email" id="email" placeholder="Test email">
            <div id="email-error-text" class="error-text"></div>
            
            <input type="text" id="role" placeholder="Test role">
            <div id="role-error-text" class="error-text"></div>
        </div>
    </div>

    <script>
        // Mock Firebase for testing
        window.db = {
            collectionGroup: () => ({
                where: () => ({
                    get: () => Promise.resolve({ empty: true })
                })
            }),
            collection: () => ({
                get: () => Promise.resolve({ docs: [] })
            })
        };

        // Mock common roles
        window.commonRoles = ['CEO', 'Manager', 'Developer', 'Designer'];

        // Mock fetch for role validation
        window.fetch = async (url, options) => {
            if (url === '/api/validate-role') {
                const body = JSON.parse(options.body);
                const role = body.role.toLowerCase();
                
                // Simulate API response
                if (role.includes('invalid') || role.includes('test')) {
                    return {
                        json: () => Promise.resolve({ isValid: false })
                    };
                } else {
                    return {
                        json: () => Promise.resolve({ isValid: true })
                    };
                }
            }
        };

        function addTestResult(containerId, message, isSuccess) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            result.textContent = message;
            container.appendChild(result);
        }

        async function testEmailFormat() {
            const container = document.getElementById('email-test-results');
            container.innerHTML = '';

            try {
                // Test invalid email format
                const result1 = await validateEmailOnSubmit('invalid-email');
                addTestResult('email-test-results', 
                    `Invalid email format test: ${result1.isValid ? 'FAILED' : 'PASSED'}`, 
                    !result1.isValid);

                // Test valid email format (but not invited)
                const result2 = await validateEmailOnSubmit('<EMAIL>');
                addTestResult('email-test-results', 
                    `Valid email format test: ${result2.errorType === 'invitation' ? 'PASSED' : 'FAILED'}`, 
                    result2.errorType === 'invitation');

            } catch (error) {
                addTestResult('email-test-results', `Error: ${error.message}`, false);
            }
        }

        async function testRoleValidation() {
            const container = document.getElementById('role-test-results');
            container.innerHTML = '';

            try {
                // Test common role
                const result1 = await validateRoleOnSubmit('CEO');
                addTestResult('role-test-results', 
                    `Common role test: ${result1.isValid ? 'PASSED' : 'FAILED'}`, 
                    result1.isValid);

                // Test invalid role
                const result2 = await validateRoleOnSubmit('invalid-role-test');
                addTestResult('role-test-results', 
                    `Invalid role test: ${result2.isValid ? 'FAILED' : 'PASSED'}`, 
                    !result2.isValid);

                // Test valid uncommon role
                const result3 = await validateRoleOnSubmit('Software Engineer');
                addTestResult('role-test-results', 
                    `Valid uncommon role test: ${result3.isValid ? 'PASSED' : 'FAILED'}`, 
                    result3.isValid);

            } catch (error) {
                addTestResult('role-test-results', `Error: ${error.message}`, false);
            }
        }

        function testErrorDisplay() {
            const container = document.getElementById('error-test-results');
            container.innerHTML = '';

            try {
                // Test email error display
                showEmailValidationError('Test email error message');
                addTestResult('error-test-results', 'Email error display test: PASSED', true);

                // Test role error display
                setTimeout(() => {
                    showRoleValidationError('Test role error message');
                    addTestResult('error-test-results', 'Role error display test: PASSED', true);
                }, 1000);

                // Test error clearing
                setTimeout(() => {
                    clearEmailValidationError();
                    clearRoleValidationError();
                    addTestResult('error-test-results', 'Error clearing test: PASSED', true);
                }, 2000);

            } catch (error) {
                addTestResult('error-test-results', `Error: ${error.message}`, false);
            }
        }
    </script>

    <!-- Load validation functions -->
    <script src="public/validationFunctions.js"></script>
    
    <!-- Add CSS for error text -->
    <style>
        .error-text {
            color: #dc2626;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            margin-bottom: 0.5rem;
            display: none;
            line-height: 1.4;
        }

        .error-text.show {
            display: block;
            animation: fadeInDown 0.3s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .input-error {
            border-color: #dc2626 !important;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake {
            animation: shake 0.6s ease-in-out;
        }
    </style>
</body>
</html>
